'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import IntakeToggle from './intake-toggle';

type IntakeMode = 'building' | 'executing';

interface SmartIntakePromptProps {
  mode: IntakeMode;
  onModeChange: (mode: IntakeMode) => void;
}

export default function SmartIntakePrompt({ mode, onModeChange }: SmartIntakePromptProps) {
  const { data: session } = useSession();
  const [input, setInput] = useState('');

  // Determine if toggle should be shown (only for non-logged-in users)
  const showToggle = !session?.user;

  const handleSubmit = async () => {
    if (!input.trim()) return;

    const encodedPrompt = encodeURIComponent(input);

    // Check if user is logged in
    if (!session?.user) {
      // Redirect to login with worksheet redirect
      window.location.href = `/auth/login?redirect=/app/worksheet&prompt=${encodedPrompt}`;
      return;
    }

    // Use embedded AI agent to interpret the intent and trigger routing for building/executing
    if (mode === 'building' || mode === 'executing') {
      try {
        const res = await fetch('/api/ai-intake/agent', {
          method: 'POST',
          body: JSON.stringify({ prompt: input, user: session.user }),
          headers: { 'Content-Type': 'application/json' }
        });

        const data = await res.json();
        const funcCall = data?.function_call;

        if (funcCall?.name === 'create_custom_proposal') {
          const args = JSON.parse(funcCall.arguments);
          const title = args.title || input;
          const category = args.category || '';
          window.location.href = `/freelancer-dashboard/proposals/create?fromPrompt=${encodeURIComponent(title)}&type=${encodeURIComponent(category)}`;
          return;
        }

        if (funcCall?.name === 'list_product') {
          const args = JSON.parse(funcCall.arguments);
          const title = args.productTitle || input;
          const category = args.category || '';
          window.location.href = `/freelancer-dashboard/storefront/list-product?fromPrompt=${encodeURIComponent(title)}&category=${encodeURIComponent(category)}`;
          return;
        }

        if (funcCall?.name === 'generate_invoice') {
          const args = JSON.parse(funcCall.arguments);
          window.location.href = `/freelancer-dashboard/projects-and-invoices/create-invoice?fromPrompt=${encodeURIComponent(args.projectTitle || input)}&amount=${args.amount}`;
          return;
        }

        if (funcCall?.name === 'post_gig') {
          const args = JSON.parse(funcCall.arguments);
          window.location.href = `/commissioner-dashboard/post-gig?title=${encodeURIComponent(args.title)}&scope=${encodeURIComponent(args.scope)}`;
          return;
        }

        if (funcCall?.name === 'browse_freelancers') {
          const args = JSON.parse(funcCall.arguments);
          const skillQuery = args.skills.join(',');
          window.location.href = `/commissioner-dashboard/freelancers?skills=${encodeURIComponent(skillQuery)}`;
          return;
        }

        // Fallback to worksheet if no known function matched
        window.location.href = `/app/worksheet?prompt=${encodedPrompt}`;
        return;
      } catch (err) {
        console.error('[AI_AGENT_ROUTING_ERROR]', err);
        // Fallback to worksheet on failure
        window.location.href = `/app/worksheet?prompt=${encodedPrompt}`;
        return;
      }
    }

    // fallback for other modes (shouldn't hit)
    window.location.href = `/app/worksheet?prompt=${encodedPrompt}`;
  };

  return (
    <div className="w-full max-w-2xl mx-auto max-h-[80vh] overflow-y-auto">
      {/* Prompt Box */}
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg p-4 border border-white/30 relative z-10">
        {/* Heading */}
        <div className="text-center mb-4">
          <h1 className="text-4xl font-bold text-gray-900 mb-1" style={{ fontFamily: 'Plus Jakarta Sans' }}>
            For builders and creators.
          </h1>
          <p className="text-sm font-light text-gray-600">
            Start something bold. Find work worth doing.
          </p>
        </div>
        {/* Centered Toggle - only show for non-logged-in users */}
        {showToggle && (
          <div className="flex justify-center mb-4">
            <IntakeToggle mode={mode} onChange={onModeChange} />
          </div>
        )}

        {/* Main Input */}
        <div className="space-y-4">
          <div className="relative">
            <textarea
              className="w-full p-3 pr-20 border-2 border-black rounded-xl text-sm placeholder-gray-500 focus:border-black focus:outline-none resize-none transition-all bg-gradient-to-r from-white to-gray-50"
              rows={2}
              placeholder={
                mode === 'building'
                  ? 'What are you trying to build?'
                  : 'What kind of work are you looking for?'
              }
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  if (input.trim()) {
                    handleSubmit();
                  }
                }
              }}
              disabled={false}
            />
            {/* Embedded Submit Button */}
            <button
              className="absolute bottom-2 right-2 bg-black text-white px-3 py-1.5 rounded-lg text-xs font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => handleSubmit()}
              disabled={!input}
            >
              Search
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}